import React from "react"
import {
  ComponentRules,
  UsageGuidelines,
} from "@/components"
import { CategoryMenuItem } from "@apollo/storefront"
import { Typography } from "@apollo/ui"
import { Shop, Shopping, ShoppingCart, Gift, Tag, Star } from "@design-systems/apollo-icons"
import {
  ArgTypes,
  Description,
  Primary,
  Source,
  Stories,
  Subtitle,
  Title,
} from "@storybook/addon-docs/blocks"
import type { Meta, StoryObj } from "@storybook/react"

import "@apollo/storefront/style.css"

/**
 * CategoryMenuItem component
 *
 * The CategoryMenuItem component displays a clickable menu item with an image or icon
 * and a label. It's designed for category navigation in e-commerce and storefront
 * applications. The component supports both images and SVG icons, with automatic
 * text truncation for consistent layout.
 *
 * Notes:
 * - Supports both images (via imageSrc/imageAlt) and SVG icons (via icon prop);
 * - Automatic text truncation with line-clamp for consistent layout;
 * - Hover states and disabled states for better user experience;
 * - Flexible sizing with min/max width constraints;
 * - Built with accessibility in mind with proper alt text support.
 */
const meta = {
  title: "@apollo∕storefront/Components/Navigation/CategoryMenuItem",
  component: CategoryMenuItem,
  tags: ["autodocs"],
  globals: {
    brand: "storefront",
  },
  parameters: {
    layout: "centered",
    design: {
      type: "figma",
      url: "https://www.figma.com/design/1Ufffyr7D28j6MXLQDLSro/%F0%9F%92%9A-Apollo-Alias-Storefront?node-id=6096-7954&m=dev",
    },
    docs: {
      description: {
        component:
          "The CategoryMenuItem component displays a clickable menu item with an image or icon and a label. Perfect for category navigation in e-commerce applications with support for images, icons, and automatic text truncation.",
      },
      page: () => (
        <>
          <Title />
          <Subtitle />
          <Description />
          <Primary />
          <h3>Import</h3>
          <Source
            code={`import { CategoryMenuItem } from "@apollo/storefront"`}
            language="tsx"
          />
          <h2 id="categorymenuitem-props">Props</h2>
          <ArgTypes />
          <h2 id="categorymenuitem-usage">Best Practices</h2>
          <UsageGuidelines
            guidelines={[
              "Use CategoryMenuItem for: Category navigation, product type selection, and menu organization in e-commerce applications",
              "Keep labels short and descriptive, as text is automatically truncated to 2 lines for consistent layout",
              "Use high-quality, consistent image sizes (recommended: 32x32px) for better visual alignment",
              "Provide meaningful alt text for images to ensure accessibility for screen readers",
              "Use SVG icons for scalable, crisp graphics that work well at different sizes",
              "Consider the disabled state for categories that are temporarily unavailable",
              "Ensure sufficient color contrast between content and background for accessibility",
              "Use consistent visual style (all images or all icons) within the same menu group",
            ]}
          />
          <h2 id="categorymenuitem-accessibility">Accessibility</h2>
          <UsageGuidelines
            guidelines={[
              <>
                Always provide descriptive <code>alt</code> attributes for
                images to ensure their purpose is clear to screen reader users.
              </>,
              <>
                Use the <code>disabled</code> prop to disable menu items that are not
                currently actionable, ensuring they are not focusable.
              </>,
              <>
                Ensure labels are descriptive and meaningful, as the component
                will automatically truncate long text for visual consistency.
              </>,
              <>
                When using SVG icons, ensure they have appropriate{" "}
                <code>aria-label</code> or <code>title</code> attributes for
                screen reader accessibility.
              </>,
              <>
                Use proper semantic HTML attributes like <code>role</code> and{" "}
                <code>aria-*</code> when implementing click handlers for navigation.
              </>,
            ]}
          />
          <h2 id="categorymenuitem-examples">Examples</h2>
          <Stories title="" />
          <h2 id="categorymenuitem-dos-donts">Do's and Don'ts</h2>
          <ComponentRules
            rules={[
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
                        imageAlt="Electronics category"
                        label="Electronics"
                      />
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
                        imageAlt="Fashion category"
                        label="Fashion"
                      />
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
                        imageAlt="Home category"
                        label="Home"
                      />
                    </div>
                  ),
                  description:
                    "Use high-quality, consistent images with descriptive alt text and clear, concise labels",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=20&h=40&fit=crop"
                        imageAlt=""
                        label="Category 1 with very long text that will be truncated"
                      />
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=50&h=20&fit=crop"
                        imageAlt=""
                        label="Cat 2"
                      />
                    </div>
                  ),
                  description:
                    "Avoid inconsistent image sizes, missing alt text, and generic labels like 'Category 1'",
                },
              },
              {
                positive: {
                  component: (
                    <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
                      <CategoryMenuItem
                        icon={<Shop size={24} />}
                        label="Shop"
                      />
                      <CategoryMenuItem
                        icon={<Shopping size={24} />}
                        label="Orders"
                      />
                      <CategoryMenuItem
                        icon={<Gift size={24} />}
                        label="Gifts"
                      />
                    </div>
                  ),
                  description:
                    "Use consistent, well-designed SVG icons for scalable graphics",
                },
                negative: {
                  component: (
                    <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
                      <CategoryMenuItem
                        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
                        imageAlt="Mixed"
                        label="Image"
                      />
                      <CategoryMenuItem
                        icon={<div style={{ width: 20, height: 20, background: "#ccc" }}>?</div>}
                        label="Icon"
                      />
                    </div>
                  ),
                  description:
                    "Avoid mixing different visual styles (photos and placeholder graphics) within the same menu group",
                },
              },
            ]}
          />
        </>
      ),
    },
  },
  argTypes: {
    imageSrc: {
      control: { type: "text" },
      description: "Image source URL for the category icon.",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    imageSrcSet: {
      control: { type: "text" },
      description: "Image srcSet for responsive images.",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    imageAlt: {
      control: { type: "text" },
      description: "Alternative text for the image (required when using imageSrc).",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    icon: {
      control: false,
      description: "SVG icon or React element to display instead of an image.",
      table: {
        type: { summary: "ReactNode" },
        defaultValue: { summary: "-" },
      },
    },
    label: {
      control: { type: "text" },
      description: "Text label for the category (automatically truncated to 2 lines).",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the menu item is disabled.",
      table: {
        type: { summary: "boolean" },
        defaultValue: { summary: "false" },
      },
    },
    className: {
      control: { type: "text" },
      description: "Additional CSS class names to apply to the root element.",
      table: {
        type: { summary: "string" },
        defaultValue: { summary: "-" },
      },
    },
    onClick: {
      control: false,
      description: "Click handler for the menu item.",
      table: {
        type: { summary: "(event: MouseEvent) => void" },
        defaultValue: { summary: "-" },
      },
    },
  },
  args: {
    label: "Category",
  },
} satisfies Meta<typeof CategoryMenuItem>

export default meta

type Story = StoryObj<typeof CategoryMenuItem>

/** Default CategoryMenuItem (demonstrates basic functionality) */
export const Overview: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "Overview CategoryMenuItem with default settings. Shows a category menu item with an image and label, demonstrating the basic functionality.",
      },
    },
  },
  args: {
    imageSrc: "https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop",
    imageAlt: "Electronics category",
    label: "Electronics",
  },
}

/** CategoryMenuItem with different content types and variations */
export const Variants: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "A comprehensive showcase of CategoryMenuItem variants including images, icons, text truncation, and disabled states.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: 32,
          alignItems: "flex-start",
        }}
      >
        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            With Images
          </Typography>
          <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
            <CategoryMenuItem
              imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
              imageAlt="Electronics"
              label="Electronics"
            />
            <CategoryMenuItem
              imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
              imageAlt="Fashion"
              label="Fashion"
            />
            <CategoryMenuItem
              imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
              imageAlt="Home"
              label="Home & Garden"
            />
          </div>
        </div>

        <div style={{ display: "flex", flexDirection: "column", gap: 12 }}>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 8 }}
          >
            With Icons
          </Typography>
          <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
            <CategoryMenuItem
              icon={<Shop size={24} />}
              label="Shop"
            />
            <CategoryMenuItem
              icon={<Shopping size={24} />}
              label="Orders"
            />
            <CategoryMenuItem
              icon={<Gift size={24} />}
              label="Gifts"
            />
          </div>
        </div>
      </div>
    )
  },
}

/** CategoryMenuItem with click handlers and interactive behavior */
export const Interactive: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "CategoryMenuItem with click handlers demonstrating interactive behavior. Click on any item to see the action in the console.",
      },
    },
  },
  render: () => {
    const handleCategoryClick = (category: string) => {
      console.log(`Clicked on ${category} category`)
      // In a real application, this would navigate to the category page
    }

    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 16,
        }}
      >
        <Typography
          level="bodyLarge"
          style={{ fontWeight: "600", marginBottom: 8 }}
        >
          Interactive Category Menu
        </Typography>
        <div style={{ display: "flex", gap: 16, flexWrap: "wrap" }}>
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
            imageAlt="Electronics"
            label="Electronics"
            onClick={() => handleCategoryClick("Electronics")}
            style={{ cursor: "pointer" }}
          />
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
            imageAlt="Fashion"
            label="Fashion"
            onClick={() => handleCategoryClick("Fashion")}
            style={{ cursor: "pointer" }}
          />
          <CategoryMenuItem
            imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
            imageAlt="Home"
            label="Home & Garden"
            onClick={() => handleCategoryClick("Home & Garden")}
            style={{ cursor: "pointer" }}
          />
          <CategoryMenuItem
            icon={<Gift size={24} />}
            label="Gifts"
            onClick={() => handleCategoryClick("Gifts")}
            style={{ cursor: "pointer" }}
          />
          <CategoryMenuItem
            icon={<Tag size={24} />}
            label="Sale Items"
            onClick={() => handleCategoryClick("Sale Items")}
            style={{ cursor: "pointer" }}
          />
        </div>
        <Typography level="bodySmall" style={{ color: "#6b7280" }}>
          Open browser console to see click events
        </Typography>
      </div>
    )
  },
}

/** CategoryMenuItem in a grid layout for mobile-friendly display */
export const GridLayout: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "CategoryMenuItem arranged in a responsive grid layout, perfect for mobile category browsing with multiple items.",
      },
    },
  },
  render: () => (
    <div
      style={{
        display: "grid",
        gridTemplateColumns: "repeat(auto-fit, minmax(80px, 1fr))",
        gap: 16,
        maxWidth: 600,
      }}
    >
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
        imageAlt="Electronics"
        label="Electronics"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=60&h=60&fit=crop"
        imageAlt="Beverages"
        label="Beverages"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=60&h=60&fit=crop"
        imageAlt="Snacks"
        label="Snacks"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=60&h=60&fit=crop"
        imageAlt="Health"
        label="Health Care"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=60&h=60&fit=crop"
        imageAlt="Home"
        label="Home Goods"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1493612276216-ee3925520721?w=60&h=60&fit=crop"
        imageAlt="Pets"
        label="Pet Supplies"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=60&h=60&fit=crop"
        imageAlt="Electronics"
        label="Electronics"
      />
      <CategoryMenuItem
        imageSrc="https://images.unsplash.com/photo-1445205170230-053b83016050?w=60&h=60&fit=crop"
        imageAlt="Fashion"
        label="Fashion"
      />
    </div>
  ),
}

/** CategoryMenuItem with custom styling and advanced layouts */
export const CustomStyling: Story = {
  parameters: {
    docs: {
      description: {
        story:
          "CategoryMenuItem with custom styling demonstrating flexible design options and advanced layout patterns.",
      },
    },
  },
  render: () => {
    return (
      <div
        style={{
          display: "flex",
          flexDirection: "column",
          gap: 32,
        }}
      >
        <div>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 16 }}
          >
            Featured Categories
          </Typography>
          <div
            style={{
              display: "flex",
              gap: 12,
              padding: 16,
              backgroundColor: "#f8f9fa",
              borderRadius: 8,
              flexWrap: "wrap",
            }}
          >
            <CategoryMenuItem
              imageSrc="https://images.unsplash.com/photo-1505740420928-5e560c06d30e?w=60&h=60&fit=crop"
              imageAlt="Promotions"
              label="🔥 Hot Deals"
              style={{
                backgroundColor: "#fff5f5",
                border: "2px solid #fed7d7",
                borderRadius: 8,
              }}
            />
            <CategoryMenuItem
              imageSrc="https://images.unsplash.com/photo-**********-31a4b719223d?w=60&h=60&fit=crop"
              imageAlt="New Arrivals"
              label="✨ New Arrivals"
              style={{
                backgroundColor: "#f0f9ff",
                border: "2px solid #bae6fd",
                borderRadius: 8,
              }}
            />
            <CategoryMenuItem
              imageSrc="https://images.unsplash.com/photo-1608043152269-423dbba4e7e1?w=60&h=60&fit=crop"
              imageAlt="Best Sellers"
              label="⭐ Best Sellers"
              style={{
                backgroundColor: "#fffbeb",
                border: "2px solid #fed7aa",
                borderRadius: 8,
              }}
            />
          </div>
        </div>

        <div>
          <Typography
            level="bodyLarge"
            style={{ fontWeight: "600", marginBottom: 16 }}
          >
            Icon-Based Navigation
          </Typography>
          <div
            style={{
              display: "flex",
              gap: 8,
              justifyContent: "space-around",
              maxWidth: 400,
            }}
          >
            <CategoryMenuItem
              icon={<Shop size={28} style={{ color: "#059669" }} />}
              label="Shop All"
            />
            <CategoryMenuItem
              icon={<ShoppingCart size={28} style={{ color: "#dc2626" }} />}
              label="Cart"
            />
            <CategoryMenuItem
              icon={<Gift size={28} style={{ color: "#7c3aed" }} />}
              label="Gifts"
            />
            <CategoryMenuItem
              icon={<Star size={28} style={{ color: "#f59e0b" }} />}
              label="Favorites"
            />
          </div>
        </div>
      </div>
    )
  },
}